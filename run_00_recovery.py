# run_00_recovery.py

import logging
import sys
from pathlib import Path

from app import settings
from app.database.queries import check_data_integrity, is_source_processed
from app.processing.file_manager import FileManager
from app.processing.queue_manager import TaskQueueManager
from app.processing.task_monitor import TaskMonitor


class SystemRecovery:
    """Скрипт восстановления для критических сбоев системы.
    Использует существующие компоненты вместо дублирования логики.
    """

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # Используем существующие компоненты вместо дублирования кода
        self.task_monitor = TaskMonitor()
        self.queue_manager = TaskQueueManager()
        self.file_manager = FileManager(enable_quarantine_processing=settings.QUARANTINE_PROCESSING_ENABLED)

    def setup_logging(self):
        """Настройка логирования для восстановления"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler("recovery.log", encoding="utf-8"),
            ],
        )

    def run_full_recovery(self):
        """Запускает полное восстановление системы"""
        self.logger.info("🚨 НАЧИНАЕТСЯ АВАРИЙНОЕ ВОССТАНОВЛЕНИЕ СИСТЕМЫ")

        try:
            # 0. КРИТИЧНО: Восстанавливаем SET_QUEUED_IDS для предотвращения дубликатов
            self.logger.info("0️⃣ Восстановление SET активных задач...")
            from app.ingestion.scanner import get_redis_connection, rebuild_queued_set

            redis_client = get_redis_connection()
            rebuild_queued_set(redis_client)

            # 1. Восстанавливаем зависшие задачи (используем TaskMonitor)
            self.logger.info("1️⃣ Восстановление зависших задач...")
            stale_stats = self.task_monitor.check_and_recover_stale_tasks()

            # 2. Сверяем файлы с БД (используем centralized queries)
            self.logger.info("2️⃣ Сверка файлов в /in_progress/ с БД...")
            orphan_stats = self._recover_orphaned_files()

            # 3. Очищаем поврежденные записи Redis (используем TaskQueueManager)
            self.logger.info("3️⃣ Очистка поврежденных записей Redis...")
            redis_stats = self._cleanup_redis()

            # 4. Проверяем целостность данных (используем centralized queries)
            self.logger.info("4️⃣ Проверка целостности данных...")
            integrity_stats = check_data_integrity()

            # 5. Выводим итоговый отчет
            self._print_recovery_report(stale_stats, orphan_stats, redis_stats, integrity_stats)

        except Exception as e:
            self.logger.critical(f"💥 КРИТИЧЕСКАЯ ОШИБКА ПРИ ВОССТАНОВЛЕНИИ: {e}", exc_info=True)
            sys.exit(1)

    def _recover_orphaned_files(self) -> dict:
        """Восстанавливает файлы в /in_progress/, которые не имеют соответствующих задач в Redis.
        Использует TaskQueueManager для работы с Redis и FileManager для перемещения файлов.
        """
        stats = {"found_orphans": 0, "recovered": 0, "errors": 0}

        try:
            # Получаем список всех задач в обработке из Redis (используем TaskQueueManager)
            processing_files = self._get_processing_files_from_redis()

            # Сканируем все директории in_progress
            for source_dir in settings.SOURCE_DIRS:
                dirs = settings.get_processing_directories(source_dir)
                in_progress_dir = dirs["in_progress"]

                if not in_progress_dir.exists():
                    continue

                # Находим все файлы в in_progress
                for file_path in in_progress_dir.rglob("*"):
                    if not file_path.is_file():
                        continue

                    # Вычисляем исходный путь
                    relative_path = file_path.relative_to(in_progress_dir)
                    original_path = dirs["source"] / relative_path
                    original_path_str = str(original_path.absolute())

                    # Проверяем, есть ли задача для этого файла
                    if original_path_str not in processing_files:
                        stats["found_orphans"] += 1
                        self.logger.warning(f"🏠 Найден потерянный файл: {file_path.name}")

                        try:
                            # Проверяем, не обработан ли файл уже (используем centralized queries)
                            if self._is_file_already_processed(file_path):
                                # Файл уже в БД - используем FileManager для перемещения в processed
                                from app.utils import extract_source_info_from_path

                                source_info = extract_source_info_from_path(file_path)
                                if source_info:
                                    source_dir = dirs["source"]  # Используем корневую директорию из dirs
                                    self.file_manager.move_to_processed(file_path, source_dir, source_info["source_id"])
                                    self.logger.info(f"✅ Потерянный файл перемещен в processed: {file_path.name}")
                                else:
                                    # Не удается извлечь source_id - помещаем в карантин для ручной проверки
                                    from app.processing.error_handler import QuarantineType

                                    self.file_manager.move_to_quarantine(
                                        file_path,
                                        dirs["source"],
                                        QuarantineType.INVALID,
                                        "Recovery: Cannot extract source_id from filename",
                                    )
                                    self.logger.warning(
                                        f"⚠️ Потерянный файл помещен в карантин (неопознанное имя): {file_path.name}"
                                    )
                            else:
                                # Файл не обработан - возвращаем в исходную директорию
                                original_path.parent.mkdir(parents=True, exist_ok=True)
                                file_path.rename(original_path)
                                self.logger.info(
                                    f"🔄 Потерянный файл возвращен в исходную директорию: {file_path.name}"
                                )

                            stats["recovered"] += 1

                        except Exception as e:
                            self.logger.error(f"❌ Ошибка восстановления потерянного файла {file_path}: {e}")
                            stats["errors"] += 1

        except Exception as e:
            self.logger.error(f"❌ Ошибка поиска потерянных файлов: {e}")
            stats["errors"] += 1

        return stats

    def _cleanup_redis(self) -> dict:
        """Очищает поврежденные записи в Redis.
        Использует TaskQueueManager для работы с очередями.
        """
        stats = {"malformed_tasks": 0, "cleaned": 0, "errors": 0}

        try:
            # Используем TaskQueueManager для поиска поврежденных задач
            stale_tasks = self.queue_manager.find_stale_tasks()

            malformed_count = 0
            for task in stale_tasks:
                if task.get("_malformed"):
                    malformed_count += 1

            stats["malformed_tasks"] = malformed_count

            if malformed_count > 0:
                self.logger.warning(f"🧹 Найдено {malformed_count} поврежденных задач")
                # TaskMonitor уже обрабатывает поврежденные задачи в check_and_recover_stale_tasks
                stats["cleaned"] = malformed_count

        except Exception as e:
            self.logger.error(f"❌ Ошибка очистки Redis: {e}")
            stats["errors"] += 1

        return stats

    def _get_processing_files_from_redis(self) -> set:
        """Получает список файлов в обработке из Redis через TaskQueueManager"""
        processing_files = set()
        try:
            import json

            import redis

            redis_client = redis.from_url(settings.REDIS_URL)
            processing_tasks = redis_client.lrange(settings.QUEUE_PARSING_PROCESSING, 0, -1)

            for raw_task in processing_tasks:
                try:
                    task_data = json.loads(raw_task.decode("utf-8"))
                    file_path = task_data.get("file_path")
                    if file_path:
                        processing_files.add(file_path)
                except json.JSONDecodeError:
                    continue

        except Exception as e:
            self.logger.error(f"❌ Ошибка получения задач из Redis: {e}")

        return processing_files

    def _is_file_already_processed(self, file_path: Path) -> bool:
        """Проверяет, обработан ли файл уже.
        Использует УНИВЕРСАЛЬНУЮ утилиту extract_source_info_from_path.
        """
        from app.utils import extract_source_info_from_path

        try:
            # Используем централизованную утилиту (устранено дублирование)
            source_info = extract_source_info_from_path(file_path)
            if not source_info:
                return False

            # Используем централизованную проверку
            return is_source_processed(source_info["source_type"], source_info["source_id"])

        except Exception as e:
            self.logger.error(f"❌ Ошибка проверки обработанности файла {file_path}: {e}")
            return False

    # УСТРАНЕНО ДУБЛИРОВАНИЕ: используется FileManager.move_to_processed() вместо собственной реализации

    def _print_recovery_report(
        self,
        stale_stats: dict,
        orphan_stats: dict,
        redis_stats: dict,
        integrity_stats: dict,
    ):
        """Выводит итоговый отчет о восстановлении"""
        self.logger.info("=" * 60)
        self.logger.info("📊 ИТОГОВЫЙ ОТЧЕТ О ВОССТАНОВЛЕНИИ СИСТЕМЫ")
        self.logger.info("=" * 60)

        # Статистика восстановления SET активных задач
        self.logger.info("🔄 SET активных задач: восстановлен из всех очередей")

        # Статистика зависших задач
        self.logger.info("🔧 Зависшие задачи:")
        self.logger.info(f"   - Восстановлено: {stale_stats.get('recovered', 0)}")
        self.logger.info(f"   - В карантин: {stale_stats.get('quarantined', 0)}")
        self.logger.info(f"   - Ошибок: {stale_stats.get('errors', 0)}")

        # Статистика потерянных файлов
        self.logger.info("🏠 Потерянные файлы:")
        self.logger.info(f"   - Найдено: {orphan_stats.get('found_orphans', 0)}")
        self.logger.info(f"   - Восстановлено: {orphan_stats.get('recovered', 0)}")
        self.logger.info(f"   - Ошибок: {orphan_stats.get('errors', 0)}")

        # Статистика Redis
        self.logger.info("🧹 Redis:")
        self.logger.info(f"   - Поврежденных задач: {redis_stats.get('malformed_tasks', 0)}")
        self.logger.info(f"   - Очищено: {redis_stats.get('cleaned', 0)}")
        self.logger.info(f"   - Ошибок: {redis_stats.get('errors', 0)}")

        # Статистика целостности данных
        self.logger.info("📖 Целостность данных:")
        self.logger.info(f"   - Всего книг: {integrity_stats.get('total_books', 0)}")
        self.logger.info(f"   - Потерянных источников: {integrity_stats.get('orphaned_sources', 0)}")
        self.logger.info(f"   - Потерянных авторов: {integrity_stats.get('orphaned_authors', 0)}")
        self.logger.info(f"   - Ошибок проверки: {integrity_stats.get('errors', 0)}")

        # Итоговый статус
        total_errors = (
            stale_stats.get("errors", 0)
            + orphan_stats.get("errors", 0)
            + redis_stats.get("errors", 0)
            + integrity_stats.get("errors", 0)
        )

        if total_errors == 0:
            self.logger.info("✅ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО УСПЕШНО")
        else:
            self.logger.warning(f"⚠️ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО С {total_errors} ОШИБКАМИ")

        self.logger.info("=" * 60)

        # Вывод статистики файлов
        file_stats = self.task_monitor.get_processing_files_stats()
        self.logger.info("📁 Состояние файловой системы после восстановления:")
        for detail in file_stats.get("details", []):
            source_name = detail.get("source_name", "unknown")
            self.logger.info(f"   {source_name}:")
            self.logger.info(f"      - В обработке: {detail.get('in_progress', 0)}")
            self.logger.info(f"      - Обработано: {detail.get('processed', 0)}")
            self.logger.info(f"      - В карантине: {detail.get('quarantine', 0)}")


def main():
    """Запуск скрипта восстановления"""
    recovery = SystemRecovery()
    recovery.run_full_recovery()


if __name__ == "__main__":
    main()
