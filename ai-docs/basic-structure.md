# Базовая структура проекта

## Обзор

Система представляет собой масштабируемый конвейер обработки электронных книг с архитектурой на основе очередей задач. Поддерживает извлечение метаданных из различных форматов (FB2, EPUB), работает с PostgreSQL для хранения данных и Redis для управления очередями.

## Корневая структура

```
├── app/                    # Основной код приложения
├── doc/                    # Документация проекта
├── tools/                  # Инструменты анализа и диагностики
├── run_*.py               # Исполняемые скрипты
├── pyproject.toml         # Конфигурация проекта и линтеров
└── docker-compose.yml     # Инфраструктура (PostgreSQL, Redis)
```

## Модульная архитектура (app/)

### Основные модули

- **`app/settings.py`** - Централизованная конфигурация системы: подключение к БД, Redis, пути к источникам
- **`app/utils.py`** - Общие утилиты для работы с файлами и метаданными

### app/ingestion/ - Обнаружение и сканирование источников

- **`scanner_inventorizer.py`** - Инвентаризация источников, дедупликация и создание задач с полными метаданными архива. Использует Redis `SET_QUEUED_IDS` + PostgreSQL для проверки дублей.

### app/processing/ - Ядро обработки данных

#### Управление задачами
- **`queue_manager.py`** - Управление очередями Redis (см. [../doc/redis_queues.md](../doc/redis_queues.md))
- **`task_monitor.py`** - Мониторинг и восстановление зависших задач
- **`error_handler.py`** - Обработка ошибок и retry-логика

#### Обработка файлов
- **`book_processor.py`** - Основной координатор обработки книг
- **`archive_processor.py`** - Обработка архивов (ZIP)
- **`parser_dispatcher.py`** - Диспетчер парсеров по типам файлов
- **`file_manager.py`** - Управление перемещением файлов.  
  > ⚠️ Компонент будет упразднён после завершения рефакторинга pipeline_20. Его обязанности перейдут в `TaskQueueManager` и `BookWorker`.

#### Извлечение и трансформация данных
- **`canonical_model.py`** - Каноническая модель книги
- **`book_data_builder.py`** - Построение структуры данных книги
- **`date_extractor.py`** - Извлечение и нормализация дат
- **`hash_computer.py`** - Вычисление хэшей для дедупликации

#### Сохранение результатов
- **`database_saver.py`** - Сохранение в PostgreSQL
- **`artifact_saver.py`** - Сохранение артефактов (JSON, тексты)

#### Специализированные компоненты
- **`fragment_detector.py`** - Определение фрагментов книг
- **`pruner.py`** - Очистка и оптимизация данных
- **`dto.py`** - Объекты передачи данных

### app/processing/parsers/ - Парсеры форматов

#### FB2 формат (app/processing/parsers/fb2/)
- **`fb2_parser.py`** - Основной парсер FB2 XML
- **`fb2_transformer.py`** - Трансформация FB2 в каноническую модель
- **`fb2_model.py`** - Модель данных FB2

### app/database/ - Слой данных

- **`connection.py`** - Подключение к PostgreSQL
- **`queries.py`** - SQL запросы и операции с БД
- **`shema.sql`** - Схема базы данных
- **`run__init_db.py`** - Инициализация БД

## Исполняемые скрипты

### Основные процессы
- **`run_10_scan_sources.py`** - Сканирование источников и постановка задач в очередь
- **`run_20_process_book_worker.py`** - Воркер обработки книг из очереди
- **`run_00_recovery.py`** - Восстановление системы после сбоев

## Конфигурация

### Зависимости и настройки
- **`pyproject.toml`** - Конфигурация Ruff (линтер), MyPy (типы), зависимости проекта
- **`requirements.txt`** - Основные зависимости Python
- **`requirements-dev.txt`** - Зависимости для разработки

### Инфраструктура
- **`docker-compose.yml`** - PostgreSQL и Redis в контейнерах
- **`.env`** (не в репозитории) - Переменные окружения: БД, пути источников

## Директории обработки

Для каждого источника создаются директории:
- **`source/`** - Исходные файлы
- **`in_progress/`** - Файлы в процессе обработки  
  _TODO: каталог планируется удалить после полного перехода на статус-по-очередям_
- **`processed/`** - Успешно обработанные файлы  
- **`quarantine/`** - Файлы с ошибками

## Инструменты (tools/)

Содержит инструменты для анализа, диагностики и мониторинга:
- Анализаторы структуры книг
- Диагностика Redis и очередей
- Мониторинг производительности

## Документация (doc/)

- **`README.md`** - Основная документация
- **`passport.md`** - Технический паспорт проекта
- **`CHANGELOG.md`** - История изменений
- **`roadmap.md`** - План развития

## Потоки данных

1. **Сканирование**: `scanner.py` → очереди Redis
2. **Обработка**: воркер → парсеры → каноническая модель → сохранение
3. **Восстановление**: мониторинг → восстановление зависших задач

Система спроектирована для горизонтального масштабирования через запуск множественных воркеров. 