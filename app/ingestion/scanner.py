# app/ingestion/scanner.py

import json
import logging
from typing import Any, Optional, cast

import redis

from app import settings
from app.database.connection import get_db_connection
from app.database.queries import is_source_processed
from app.processing.error_handler import ErrorType, ProcessingError
from app.utils import extract_source_id


def get_redis_connection() -> redis.Redis:
    """Получение соединения с Redis"""
    return redis.from_url(settings.REDIS_URL)


def sync_redis_with_db(redis_client: redis.Redis):
    """Синхронизирует Redis кэш с PostgreSQL для ускорения проверок.

    ИСПОЛЬЗОВАТЬ ТОЛЬКО ДЛЯ БОЛЬШИХ ДАМПОВ (2000+ файлов)!

    СТРОГИЙ РЕЖИМ:
    - book_sources является единственным источником правды об успешно обработанных книгах
    - completed очередь НЕ используется для синхронизации (может содержать задачи без записей в БД)
    - строгий режим выявляет проблемы в транзакциях воркера вместо их сокрытия

    Загружает в SET_PROCESSED только записи из PostgreSQL book_sources.

    ОПТИМИЗИРОВАНО ДЛЯ БОЛЬШИХ ОБЪЕМОВ:
    - Использует серверные курсоры для PostgreSQL (не загружает всё в память)
    - Обрабатывает данные батчами по 10k записей
    - Минимизирует пиковое потребление памяти Python процессом

    Для ежедневного сканирования (10-20 файлов) избыточно:
    - Загрузка 3 млн записей = 30 секунд
    - Проверка 20 файлов в PostgreSQL = 40ms

    Рациональность:
    - При дампе 2500 книг: экономия на дубликатах оправдывает загрузку
    - При ежедневном сканировании: overhead в 750x больше полезной работы

    ЗАЩИТА ОТ ГОНКИ СОСТОЯНИЙ:
    - Использует блокировку Redis SETNX для предотвращения параллельной синхронизации
    - Блокирует добавление новых задач воркерами на время синхронизации
    """
    sync_lock_key = "sync:lock"
    sync_lock_timeout = 300  # 5 минут максимум на синхронизацию

    try:
        logging.info("🔄 Синхронизация Redis кэша с PostgreSQL (СТРОГИЙ РЕЖИМ)...")

        # Получаем блокировку для предотвращения гонки состояний
        if not redis_client.set(sync_lock_key, "1", nx=True, ex=sync_lock_timeout):
            logging.warning("⚠️ Синхронизация уже выполняется другим процессом. Пропуск.")
            return

        db_count = 0
        redis_batch_size = 1000  # Размер батча для Redis операций
        db_batch_size = 10000  # Размер батча для чтения из PostgreSQL

        # Очищаем старый кэш ПОСЛЕ получения блокировки
        redis_client.delete(settings.SET_PROCESSED)

        # Загружаем ТОЛЬКО из PostgreSQL батчами через серверный курсор
        with get_db_connection() as conn:
            # Используем именованный курсор для серверной обработки с RealDictCursor
            from psycopg.rows import dict_row

            with conn.cursor(name="sync_cursor_db", row_factory=dict_row) as cur:
                cur.execute("SELECT source_type, source_id FROM book_sources")

                while True:
                    # Читаем батч записей
                    rows = cur.fetchmany(db_batch_size)
                    if not rows:
                        break

                    # Формируем ключи для текущего батча
                    batch_keys = [f"processed:{row['source_type']}:{row['source_id']}" for row in rows]
                    db_count += len(batch_keys)

                    # Отправляем в Redis батчами по redis_batch_size
                    for i in range(0, len(batch_keys), redis_batch_size):
                        redis_batch = batch_keys[i : i + redis_batch_size]
                        redis_client.sadd(settings.SET_PROCESSED, *redis_batch)

                    # Логируем прогресс для больших объемов
                    if db_count % 100000 == 0:
                        logging.info(f"📊 Обработано {db_count} записей из БД...")

        if db_count > 0:
            logging.info(f"✅ Синхронизирован кэш: {db_count} записей из БД")

            # Показываем потребление памяти
            try:
                memory_usage_raw = redis_client.memory_usage(settings.SET_PROCESSED) or 0
                memory_usage = cast(int, memory_usage_raw)
                logging.info(f"📊 Использование памяти кэша: {memory_usage / 1024 / 1024:.1f} MB")
            except Exception:
                # memory_usage может не поддерживаться в старых версиях Redis
                logging.info("📊 Кэш синхронизирован успешно")
        else:
            logging.info("✅ Кэш очищен (нет обработанных файлов)")

    except Exception as e:
        logging.error(f"❌ Ошибка синхронизации кэша: {e}")
        # Продолжаем работу без кэша - будем проверять только БД
        raise
    finally:
        # Освобождаем блокировку в любом случае
        try:
            redis_client.delete(sync_lock_key)
        except Exception:
            pass  # Игнорируем ошибки освобождения блокировки


def clear_redis_cache(redis_client: redis.Redis):
    """Очищает Redis кэш обработанных файлов.

    Используется для освобождения памяти после обработки больших дампов
    или при переходе на ежедневный режим работы.
    """
    try:
        deleted_count = redis_client.delete(settings.SET_PROCESSED)
        if deleted_count:
            logging.info(f"🗑️ Удален кэш: освобождено место для {deleted_count} ключей")
        else:
            logging.info("🗑️ Кэш уже пуст")
    except Exception as e:
        logging.error(f"❌ Ошибка очистки кэша: {e}")
        raise


def rebuild_queued_set(redis_client: redis.Redis):
    """Восстанавливает SET активных задач из ВСЕХ очередей (new/processing/completed).

    КРИТИЧНО: Включает completed очередь для предотвращения дубликатов в случае падения
    воркера после завершения обработки, но до записи в PostgreSQL.

    ОПТИМИЗАЦИЯ ПАМЯТИ: Использует batch-обработку для больших очередей (100k+ элементов)
    вместо загрузки всех данных в память Python процесса.

    Используется при запуске системы для синхронизации.

    📋 Документация очередей: doc/redis_queues.md
    """
    try:
        # Очищаем старый SET
        redis_client.delete(settings.SET_QUEUED_IDS)

        task_keys = set()
        batch_size = 1000  # Обрабатываем по 1000 задач для оптимизации памяти

        # Список очередей для обработки
        queues = [
            (settings.QUEUE_PARSING_NEW, "new"),
            (settings.QUEUE_PARSING_PROCESSING, "processing"),
            (settings.QUEUE_COMPLETED, "completed"),
        ]

        for queue_name, queue_type in queues:
            queue_length_raw = redis_client.llen(queue_name)
            queue_length = cast(int, queue_length_raw)  # Явное приведение типа для mypy

            if queue_length == 0:
                continue

            logging.info(f"🔄 Обработка очереди {queue_type}: {queue_length} элементов")

            # Обрабатываем очередь батчами для экономии памяти
            for start_idx in range(0, queue_length, batch_size):
                end_idx = min(start_idx + batch_size - 1, queue_length - 1)

                # Загружаем только текущий batch вместо всей очереди
                batch_tasks_raw = redis_client.lrange(queue_name, start_idx, end_idx)
                batch_tasks = cast(list, batch_tasks_raw)

                # Извлекаем ключи из текущего batch
                for task_bytes in batch_tasks:
                    try:
                        task_data = json.loads(task_bytes.decode("utf-8"))
                        source_type = task_data.get("source_type")
                        source_id = task_data.get("source_id")
                        if source_type and source_id:
                            task_keys.add(f"{source_type}:{source_id}")
                    except json.JSONDecodeError:
                        continue

                # Логируем прогресс для больших очередей
                if queue_length > 10000 and (start_idx + batch_size) % 10000 == 0:
                    logging.info(f"📊 Обработано {start_idx + batch_size}/{queue_length} из очереди {queue_type}")

        # Массово добавляем в SET батчами для Redis
        if task_keys:
            redis_batch_size = 5000  # Оптимальный размер для Redis SADD
            task_keys_list = list(task_keys)

            for i in range(0, len(task_keys_list), redis_batch_size):
                redis_batch = task_keys_list[i : i + redis_batch_size]
                redis_client.sadd(settings.SET_QUEUED_IDS, *redis_batch)

        logging.info(
            f"✅ SET активных задач восстановлен из всех 3 очередей. Добавлено уникальных ключей: {len(task_keys)}"
        )

    except Exception as e:
        logging.error(f"❌ Ошибка восстановления SET активных задач: {e}")
        raise


def is_file_cached_as_processed(redis_client: redis.Redis, source_type: int, source_id: int) -> Optional[bool]:
    """Быстрая проверка в Redis кэше.

    Returns:
        True - файл обработан (есть в кэше)
        False - файла нет в кэше
        None - кэш недоступен, нужна проверка БД

    """
    try:
        cache_key = f"processed:{source_type}:{source_id}"
        result = redis_client.sismember(settings.SET_PROCESSED, cache_key)
        return cast(bool, result)
    except Exception as e:
        logging.warning(f"⚠️ Ошибка доступа к Redis кэшу: {e}")
        return None


def add_to_processed_cache(redis_client: redis.Redis, source_type: int, source_id: int):
    """Добавляет обработанный файл в кэш (если кэш активен)"""
    try:
        cache_key = f"processed:{source_type}:{source_id}"
        redis_client.sadd(settings.SET_PROCESSED, cache_key)
    except Exception as e:
        logging.warning(f"⚠️ Ошибка обновления кэша: {e}")


def add_to_queued_set(redis_client: redis.Redis, source_type: int, source_id: int):
    """Добавляет задачу в SET активных задач для быстрой проверки дубликатов"""
    try:
        task_key = f"{source_type}:{source_id}"
        redis_client.sadd(settings.SET_QUEUED_IDS, task_key)
    except Exception as e:
        logging.warning(f"⚠️ Ошибка добавления в SET очередей: {e}")


def is_task_already_queued(redis_client: redis.Redis, source_type: int, source_id: int) -> bool:
    """ОПТИМИЗИРОВАННАЯ О(1) проверка задач в очередях через Redis SET.

    Args:
        redis_client: Подключение к Redis
        source_type: Тип источника
        source_id: ID файла

    Returns:
        True если задача уже есть в new, processing или completed очередях

    Raises:
        Exception: При критических ошибках Redis SET

    """
    try:
        # O(1) операция - проверяем SET
        task_key = f"{source_type}:{source_id}"
        result = redis_client.sismember(settings.SET_QUEUED_IDS, task_key)
        return cast(bool, result)

    except Exception as e:
        logging.error(f"❌ Критическая ошибка проверки SET очередей для {source_type}:{source_id}: {e}")
        # Выбрасываем исключение - система должна восстановить SET через rebuild_queued_set
        raise


def scan_and_register_new_files(use_cache: bool = False):
    """Главная функция сканера-продюсера:
    1. Опционально синхронизирует Redis кэш с PostgreSQL
    2. Сканирует исходные директории из SOURCE_DIRS
    3. Для каждого ZIP файла извлекает source_id (цифры из имени)
    4. Проверяет (source_type, source_id) в PostgreSQL/кэше и очередях Redis
    5. Если файл новый - создает задачу и добавляет в очередь

    Args:
        use_cache: Если True, загружает все обработанные файлы в Redis для ускорения
                  Рекомендуется только для больших дампов (2000+ файлов)

    ВАЖНО:
    - Сканер НЕ перемещает файлы и НЕ удаляет их
    - Проверяет дубликаты в PostgreSQL И в очередях Redis

    """
    redis_client = get_redis_connection()
    total_found = 0
    total_new = 0
    total_skipped_no_id = 0
    total_skipped_processed = 0
    total_skipped_queued = 0

    try:
        # Синхронизируем кэш только если явно запрошено
        if use_cache:
            logging.info("🗄️ Кэширование обработанных книг...")
            sync_redis_with_db(redis_client)
            logging.info("📂 Сканирование с кэшем...")
        else:
            logging.info("📂 Сканирование источников...")

        for source_dir in settings.SOURCE_DIRS:
            if not source_dir.is_dir():
                logging.warning(f"⚠️ Директория источника не найдена, пропускаем: {source_dir}")
                continue

            source_name = source_dir.name
            source_type = settings.SOURCE_TYPE_MAP.get(source_name)

            if source_type is None:
                logging.warning(f"⚠️ Неизвестный тип источника для директории '{source_name}', пропускаем.")
                continue

            # Сканируем только ZIP файлы в корне директории (НЕ рекурсивно)
            for file_path in source_dir.glob("*.zip"):
                total_found += 1

                # Извлекаем source_id из имени файла
                source_id = extract_source_id(file_path)
                if source_id is None:
                    total_skipped_no_id += 1
                    logging.debug(f"⏭️ Пропускаем файл без ID: {file_path.name}")
                    continue

                # Проверяем дубликаты - ОПТИМАЛЬНЫЙ ПОРЯДОК (быстрое → медленное)

                # 1. БЫСТРО: Проверяем очереди Redis (new/processing/completed)
                is_already_queued = is_task_already_queued(redis_client, source_type, source_id)
                if is_already_queued:
                    total_skipped_queued += 1
                    logging.debug(f"⏭️ Задача уже в очереди: {source_type}:{source_id}")
                    continue

                # 2. БЫСТРО: Проверяем кэш Redis SET_PROCESSED (если включен)
                is_already_processed = False

                if use_cache:
                    cached_result = is_file_cached_as_processed(redis_client, source_type, source_id)

                    if cached_result is True:
                        is_already_processed = True
                        total_skipped_processed += 1
                        logging.debug(f"⏭️ Файл уже обработан (кэш): {source_type}:{source_id}")
                        continue
                    elif cached_result is None:
                        # Fallback на БД если кэш недоступен
                        logging.debug(f"⚠️ Кэш недоступен, проверяем БД: {source_type}:{source_id}")

                # 3. МЕДЛЕННО: Проверяем PostgreSQL (только если нет кэша или он недоступен)
                if not use_cache or cached_result is None:
                    try:
                        is_already_processed = is_source_processed(source_type, source_id)
                        if is_already_processed:
                            total_skipped_processed += 1
                            logging.debug(f"⏭️ Файл уже обработан (БД): {source_type}:{source_id}")
                            continue
                    except Exception as e:
                        # При ошибках БД логируем и пропускаем файл (безопаснее)
                        if isinstance(e, ProcessingError) and e.error_type == ErrorType.RETRY:
                            logging.error(f"❌ Ошибка проверки БД для {source_type}:{source_id}: {e.message}")
                            logging.warning(f"⏭️ Пропускаем файл из-за проблем с БД: {source_type}:{source_id}")
                            continue
                        else:
                            # Неожиданная ошибка - пробрасываем дальше
                            raise

                # Формируем задачу с минимальными данными (ОПТИМАЛЬНО)
                task_data: dict[str, Any] = {
                    "source_type": source_type,
                    "source_id": source_id,
                    "filename": file_path.name,  # Только имя файла, путь восстановит воркер
                }

                try:
                    # Атомарно добавляем задачу в очередь И в SET для быстрой проверки
                    pipe = redis_client.pipeline()
                    pipe.lpush(settings.QUEUE_PARSING_NEW, json.dumps(task_data))
                    pipe.sadd(settings.SET_QUEUED_IDS, f"{source_type}:{source_id}")
                    pipe.execute()

                    total_new += 1
                    logging.debug(f"✅ Добавлена новая задача: {source_type}:{source_id} ({file_path.name})")

                except Exception as e:
                    logging.error(f"❌ Ошибка при добавлении задачи для файла {file_path}: {e}")

        logging.info(
            f"🏁 Сканирование завершено. "
            f"Найдено: {total_found}, "
            f"Новых задач: {total_new}, "
            f"Пропущено без ID: {total_skipped_no_id}, "
            f"Пропущено обработанных: {total_skipped_processed}, "
            f"Пропущено в очереди: {total_skipped_queued}"
        )

        # Рекомендации по использованию кэша
        if not use_cache and total_new > 1000:
            logging.info("💡 Совет: для больших дампов используйте --cache для ускорения")
        elif use_cache and total_new < 100:
            logging.info("💡 Совет: для ежедневного сканирования кэш избыточен, используйте --clear-cache")

    except redis.RedisError as e:
        logging.error(f"❌ Ошибка Redis при сканировании: {e}")
        raise
    except Exception as e:
        logging.error(f"❌ Неожиданная ошибка при сканировании: {e}")
        raise
