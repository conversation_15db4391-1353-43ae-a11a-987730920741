# app/processing/__init__.py

"""Модуль обработки книг для Ingestion Pipeline.

Содержит все компоненты для воркера-потребителя:
- TaskQueueManager: управление очередями Redis
- FileManager: атомарные операции с файлами
- ArchiveProcessor: распаковка архивов
- BookParser: парсинг книжных файлов
- BookProcessor: высокоуровневая обработка с дедупликацией
- DatabaseSaver: сохранение в PostgreSQL
- BookDataBuilder: подготовка DTO для DatabaseSaver
- BookDTO, BookSourceInfo: объекты передачи данных
- ErrorHandler: обработка ошибок
- TaskMonitor: мониторинг зависших задач
"""
