# app/processing/task_monitor.py

import json
import logging
import time
from pathlib import Path
from typing import Any, cast

import redis

from app import settings

from .file_manager import FileManager
from .queue_manager import TaskQueueManager


class TaskMonitor:
    """Монитор зависших задач для восстановления процессов после сбоев.
    Находит задачи в processing старше WORKER_TIMEOUT и возвращает их в очередь.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.queue_manager = TaskQueueManager()
        self.file_manager = FileManager(enable_quarantine_processing=settings.QUARANTINE_PROCESSING_ENABLED)

    def check_and_recover_stale_tasks(self) -> dict[str, int]:
        """Проверяет и восстанавливает зависшие задачи.

        Returns:
            Статистика восстановления: {'recovered': int, 'quarantined': int, 'errors': int}

        """
        self.logger.info("🔍 Начинаем проверку зависших задач...")

        stats = {"recovered": 0, "quarantined": 0, "errors": 0}

        try:
            # Находим зависшие задачи
            stale_tasks = self.queue_manager.find_stale_tasks()

            if not stale_tasks:
                self.logger.info("✅ Зависших задач не найдено")
                return stats

            self.logger.warning(f"⚠️ Найдено {len(stale_tasks)} зависших задач")

            for task in stale_tasks:
                try:
                    if task.get("_malformed"):
                        # Поврежденная задача - просто удаляем
                        self._handle_malformed_task(task)
                        stats["errors"] += 1
                    else:
                        # Пытаемся восстановить задачу
                        if self._recover_stale_task(task):
                            stats["recovered"] += 1
                        else:
                            stats["quarantined"] += 1

                except Exception as e:
                    self.logger.error(f"❌ Ошибка при восстановлении задачи: {e}")
                    stats["errors"] += 1

            self.logger.info(
                f"📊 Восстановление завершено: восстановлено={stats['recovered']}, "
                f"в карантин={stats['quarantined']}, ошибок={stats['errors']}"
            )

        except Exception as e:
            self.logger.error(f"❌ Критическая ошибка при проверке зависших задач: {e}")
            stats["errors"] += 1

        return stats

    def _recover_stale_task(self, task_data: dict[str, Any]) -> bool:
        """Восстанавливает одну зависшую задачу.

        Returns:
            True если задача успешно восстановлена, False если отправлена в карантин

        """
        file_path = task_data.get("file_path", "unknown")
        self.logger.info(f"🔧 Восстанавливаем зависшую задачу: {Path(file_path).name}")

        try:
            # Проверяем состояние файла
            original_path = Path(file_path)
            source_type = task_data.get("source_type")

            if not source_type:
                self.logger.warning(f"⚠️ Нет source_type для задачи {file_path}")
                self._cleanup_stale_task(task_data, "Отсутствует source_type")
                return False

            # Определяем директории обработки
            from app.utils import get_source_dir_by_type

            source_dir = get_source_dir_by_type(source_type)
            if not source_dir:
                self.logger.warning(f"⚠️ Неизвестный source_type: {source_type}")
                self._cleanup_stale_task(task_data, f"Неизвестный source_type: {source_type}")
                return False

            dirs = settings.get_processing_directories(source_dir)

            # Ищем файл в /in_progress/
            relative_path = original_path.relative_to(dirs["source"])
            in_progress_path = dirs["in_progress"] / relative_path

            if in_progress_path.exists():
                # Файл найден в in_progress - возвращаем в исходную директорию
                self.logger.info(f"📂 Возвращаем файл из in_progress: {in_progress_path.name}")

                # Создаем исходную директорию если не существует
                original_path.parent.mkdir(parents=True, exist_ok=True)

                # Перемещаем файл обратно
                in_progress_path.rename(original_path)

                # Возвращаем задачу в очередь
                clean_task = {k: v for k, v in task_data.items() if not k.startswith("_")}

                # Возвращаем в очередь для повторной обработки
                redis_client = redis.from_url(settings.REDIS_URL)
                pipe = redis_client.pipeline()
                pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data.get("_raw_task", ""))
                pipe.lpush(settings.QUEUE_PARSING_NEW, json.dumps(clean_task))
                pipe.execute()

                self.logger.info(f"📤 Задача возвращена в очередь для повтора: {file_path}")
                return True

            elif original_path.exists():
                # Файл уже в исходной директории - просто удаляем задачу из processing
                self.logger.info(f"📁 Файл уже в исходной директории: {original_path.name}")
                self._cleanup_stale_task(task_data, "Файл уже в исходной директории")
                return True

            else:
                # Файл не найден - возможно уже обработан или потерян
                self.logger.warning(f"❓ Файл не найден ни в исходной, ни в рабочей директории: {file_path}")
                self._cleanup_stale_task(task_data, "Файл не найден")
                return False

        except Exception as e:
            self.logger.error(f"❌ Ошибка восстановления задачи {file_path}: {e}")
            self._cleanup_stale_task(task_data, f"Ошибка восстановления: {e}")
            return False

    def _handle_malformed_task(self, task_data: dict[str, Any]):
        """Обрабатывает поврежденную задачу - удаляет из очереди и SET"""
        self.logger.warning("🗑️ Удаляем поврежденную задачу из очереди")

        try:
            # Для поврежденных задач тоже используем финализацию, если возможно
            if task_data.get("source_type") and task_data.get("source_id"):
                success = self.queue_manager.finalize_task(task_data, "поврежденная задача")
                if success:
                    self.logger.info("✅ Поврежденная задача финализирована")
                    return

            # Fallback для задач без source_type/source_id
            redis_client = redis.from_url(settings.REDIS_URL)
            redis_client.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data.get("_raw_task", ""))
            self.logger.info("✅ Поврежденная задача удалена из очереди")

        except Exception as e:
            self.logger.error(f"❌ Ошибка удаления поврежденной задачи: {e}")

    def _cleanup_stale_task(self, task_data: dict[str, Any], reason: str):
        """Очищает зависшую задачу из очереди обработки И из SET активных задач"""
        try:
            # Используем финализацию для гарантированной очистки
            success = self.queue_manager.finalize_task(task_data, f"зависшая задача: {reason}")
            if success:
                self.logger.info(f"🧹 Зависшая задача финализирована: {reason}")
            else:
                self.logger.warning(f"⚠️ Не удалось финализировать зависшую задачу: {reason}")
        except Exception as e:
            self.logger.error(f"❌ Ошибка финализации зависшей задачи: {e}")

    def get_processing_files_stats(self) -> dict[str, Any]:
        """Возвращает статистику файлов в директориях обработки.
        Полезно для мониторинга состояния системы.
        """
        stats: dict[str, Any] = {
            "in_progress": 0,
            "processed": 0,
            "quarantine": 0,
            "details": [],
        }

        try:
            for source_dir in settings.SOURCE_DIRS:
                dirs = settings.get_processing_directories(source_dir)

                source_stats: dict[str, Any] = {
                    "source_name": source_dir.name,
                    "in_progress": 0,
                    "processed": 0,
                    "quarantine": 0,
                }

                # Подсчитываем файлы в каждой директории
                for dir_type, dir_path in dirs.items():
                    if dir_type == "source":
                        continue

                    if dir_path.exists():
                        file_count = len(list(dir_path.rglob("*")))
                        source_stats[dir_type] = file_count
                        current_count = cast(int, stats[dir_type])
                        stats[dir_type] = current_count + file_count

                details_list = cast(list[dict[str, Any]], stats["details"])
                details_list.append(source_stats)

        except Exception as e:
            self.logger.error(f"❌ Ошибка получения статистики файлов: {e}")
            stats["error"] = str(e)

        return stats

    def run_continuous_monitoring(self, check_interval: int = 300):
        """Запускает непрерывный мониторинг зависших задач.

        Args:
            check_interval: Интервал проверки в секундах (по умолчанию 5 минут)

        """
        self.logger.info(f"🎯 Запуск непрерывного мониторинга с интервалом {check_interval} сек")

        try:
            while True:
                stats = self.check_and_recover_stale_tasks()

                if stats["recovered"] > 0 or stats["quarantined"] > 0 or stats["errors"] > 0:
                    # Выводим статистику файлов при наличии активности
                    file_stats = self.get_processing_files_stats()
                    self.logger.info(f"📁 Файлов в обработке: {file_stats}")

                time.sleep(check_interval)

        except KeyboardInterrupt:
            self.logger.info("⏹️ Мониторинг остановлен пользователем")
        except Exception as e:
            self.logger.error(f"❌ Критическая ошибка мониторинга: {e}")
            raise
