# app/storage/s3.py

import io
import logging
import zipfile
from typing import Iterator
from urllib.parse import urlparse

from .base import (
    ArchiveMetadata,
    StorageAccessError,
    StorageCorruptionError,
    StorageManager,
)

logger = logging.getLogger(__name__)


class S3StorageManager(StorageManager):
    """Реализация StorageManager для S3 хранилища.

    СТАТУС: Базовая реализация для будущего развития.
    В текущей версии используется только LocalStorageManager.
    """

    def __init__(
        self,
        aws_access_key_id: str = None,
        aws_secret_access_key: str = None,
        endpoint_url: str = None,
        region_name: str = "us-east-1",
    ):
        """Инициализация S3 Storage Manager.

        Args:
            aws_access_key_id: AWS Access Key ID
            aws_secret_access_key: AWS Secret Access Key
            endpoint_url: Endpoint URL для S3-совместимых хранилищ
            region_name: AWS регион
        """
        self.storage_type = "s3"
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.endpoint_url = endpoint_url
        self.region_name = region_name

        # Ленивая инициализация S3 клиента
        self._s3_client = None

    def _get_s3_client(self):
        """Получает S3 клиент с ленивой инициализацией."""
        if self._s3_client is None:
            try:
                import boto3

                self._s3_client = boto3.client(
                    "s3",
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    endpoint_url=self.endpoint_url,
                    region_name=self.region_name,
                )

            except ImportError as e:
                raise StorageAccessError(
                    "boto3 не установлен. Установите: pip install boto3",
                    storage_type=self.storage_type,
                    path="",
                ) from e

        return self._s3_client

    def _parse_s3_uri(self, s3_uri: str) -> tuple[str, str]:
        """Парсит S3 URI и возвращает bucket и key.

        Args:
            s3_uri: S3 URI в формате s3://bucket/path/to/object

        Returns:
            tuple: (bucket_name, object_key)
        """
        parsed = urlparse(s3_uri)
        if parsed.scheme != "s3":
            raise ValueError(f"Неверный S3 URI: {s3_uri}")

        bucket = parsed.netloc
        key = parsed.path.lstrip("/")

        return bucket, key

    def list_archives(self, source_path: str) -> Iterator[str]:
        """Возвращает итератор путей ко всем ZIP архивам в S3 bucket/prefix.

        Args:
            source_path: S3 URI в формате s3://bucket/prefix/

        Yields:
            str: S3 URI к архиву
        """
        try:
            s3_client = self._get_s3_client()
            bucket, prefix = self._parse_s3_uri(source_path)

            # Обеспечиваем что prefix заканчивается на /
            if prefix and not prefix.endswith("/"):
                prefix += "/"

            paginator = s3_client.get_paginator("list_objects_v2")

            for page in paginator.paginate(Bucket=bucket, Prefix=prefix, Delimiter="/"):
                for obj in page.get("Contents", []):
                    key = obj["Key"]

                    # Проверяем что это ZIP файл в корне prefix (не в подпапках)
                    relative_key = key[len(prefix) :] if prefix else key
                    if "/" not in relative_key and relative_key.lower().endswith(".zip"):
                        yield f"s3://{bucket}/{key}"

        except Exception as e:
            # Для совместимости оборачиваем все S3 ошибки
            raise StorageAccessError(
                f"Ошибка доступа к S3: {e}",
                storage_type=self.storage_type,
                path=source_path,
            ) from e

    def get_archive_metadata(self, archive_path: str) -> ArchiveMetadata:
        """Получает метаданные S3 архива.

        Args:
            archive_path: S3 URI к архиву

        Returns:
            ArchiveMetadata: Метаданные архива
        """
        try:
            s3_client = self._get_s3_client()
            bucket, key = self._parse_s3_uri(archive_path)

            response = s3_client.head_object(Bucket=bucket, Key=key)

            return ArchiveMetadata(
                archive_path=archive_path,
                archive_mtime=response["LastModified"].timestamp(),
                size_bytes=response.get("ContentLength"),
            )

        except Exception as e:
            raise StorageAccessError(
                f"Ошибка получения метаданных S3 объекта: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e

    def list_books_in_archive(self, archive_path: str) -> list[str]:
        """Возвращает список книжных файлов в S3 ZIP архиве.

        Args:
            archive_path: S3 URI к архиву

        Returns:
            list[str]: Список имен файлов
        """
        try:
            s3_client = self._get_s3_client()
            bucket, key = self._parse_s3_uri(archive_path)

            # Скачиваем архив в память
            response = s3_client.get_object(Bucket=bucket, Key=key)
            archive_data = response["Body"].read()

            book_files = []

            with zipfile.ZipFile(io.BytesIO(archive_data), "r") as zip_file:
                for file_info in zip_file.infolist():
                    # Игнорируем директории и системные файлы
                    if file_info.is_dir() or file_info.filename.startswith("."):
                        continue

                    filename = file_info.filename

                    # Ищем книжные файлы (FB2, EPUB, TXT)
                    if any(filename.lower().endswith(ext) for ext in [".fb2", ".epub", ".txt"]):
                        from pathlib import Path

                        # Возвращаем только имя файла без пути
                        book_files.append(Path(filename).name)

            logger.debug(f"Найдено {len(book_files)} книжных файлов в S3 архиве {key}")
            return book_files

        except zipfile.BadZipFile as e:
            raise StorageCorruptionError(
                f"Поврежденный ZIP архив в S3: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
        except Exception as e:
            raise StorageAccessError(
                f"Ошибка чтения S3 архива: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
