# app/storage/local.py

import logging
import zipfile
from pathlib import Path
from typing import Iterator

from .base import (
    ArchiveMetadata,
    StorageAccessError,
    StorageCorruptionError,
    StorageManager,
)

logger = logging.getLogger(__name__)


class LocalStorageManager(StorageManager):
    """Реализация StorageManager для локальной файловой системы."""

    def __init__(self):
        self.storage_type = "local"

    def list_archives(self, source_path: str) -> Iterator[str]:
        """Возвращает итератор путей ко всем ZIP архивам в локальной директории.

        Args:
            source_path: Путь к локальной директории

        Yields:
            str: Абсолютный путь к архиву
        """
        try:
            source_dir = Path(source_path)

            if not source_dir.exists():
                raise StorageAccessError(
                    f"Директория не существует: {source_path}",
                    storage_type=self.storage_type,
                    path=source_path,
                )

            if not source_dir.is_dir():
                raise StorageAccessError(
                    f"Путь не является директорией: {source_path}",
                    storage_type=self.storage_type,
                    path=source_path,
                )

            # Сканируем только корень директории (НЕ рекурсивно)
            for archive_path in source_dir.glob("*.zip"):
                if archive_path.is_file():
                    yield str(archive_path.absolute())

        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка доступа к директории: {e}",
                storage_type=self.storage_type,
                path=source_path,
            ) from e

    def get_archive_metadata(self, archive_path: str) -> ArchiveMetadata:
        """Получает метаданные локального архива.

        Args:
            archive_path: Путь к локальному архиву

        Returns:
            ArchiveMetadata: Метаданные архива
        """
        try:
            path = Path(archive_path)

            if not path.exists():
                raise StorageAccessError(
                    f"Архив не найден: {archive_path}",
                    storage_type=self.storage_type,
                    path=archive_path,
                )

            stat = path.stat()

            return ArchiveMetadata(
                archive_path=archive_path,
                archive_mtime=stat.st_mtime,
                size_bytes=stat.st_size,
            )

        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка доступа к архиву: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e

    def list_books_in_archive(self, archive_path: str) -> list[str]:
        """Возвращает список книжных файлов в локальном ZIP архиве.

        Args:
            archive_path: Путь к локальному архиву

        Returns:
            list[str]: Список имен файлов (например, ['12345.fb2'])
        """
        try:
            path = Path(archive_path)

            if not path.exists():
                raise StorageAccessError(
                    f"Архив не найден: {archive_path}",
                    storage_type=self.storage_type,
                    path=archive_path,
                )

            book_files = []

            with zipfile.ZipFile(path, "r") as zip_file:
                for file_info in zip_file.infolist():
                    # Игнорируем директории и системные файлы
                    if file_info.is_dir() or file_info.filename.startswith("."):
                        continue

                    filename = file_info.filename

                    # Ищем книжные файлы (FB2, EPUB, TXT)
                    if any(filename.lower().endswith(ext) for ext in [".fb2", ".epub", ".txt"]):
                        # Возвращаем только имя файла без пути
                        book_files.append(Path(filename).name)

            logger.debug(f"Найдено {len(book_files)} книжных файлов в {Path(archive_path).name}")
            return book_files

        except zipfile.BadZipFile as e:
            raise StorageCorruptionError(
                f"Поврежденный ZIP архив: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
        except (OSError, PermissionError) as e:
            raise StorageAccessError(
                f"Ошибка чтения архива: {e}",
                storage_type=self.storage_type,
                path=archive_path,
            ) from e
