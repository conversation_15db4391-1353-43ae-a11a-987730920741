# app/storage/base.py

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Iterator, Optional


@dataclass
class ArchiveMetadata:
    """Метаданные архива для унифицированной работы с различными хранилищами."""

    archive_path: str  # Путь к архиву (локальный путь или S3 URI)
    archive_mtime: float  # Время модификации архива (Unix timestamp)
    size_bytes: Optional[int] = None  # Размер архива в байтах (опционально)

    def __post_init__(self):
        """Валидация данных после инициализации."""
        if not self.archive_path:
            raise ValueError("archive_path не может быть пустым")
        if self.archive_mtime <= 0:
            raise ValueError("archive_mtime должно быть положительным числом")


class StorageManager(ABC):
    """Абстрактный интерфейс для работы с различными типами хранилищ.

    Инкапсулирует различия между локальной ФС и S3, предоставляя
    единый интерфейс для сканера-инвентаризатора.
    """

    @abstractmethod
    def list_archives(self, source_path: str) -> Iterator[str]:
        """Возвращает итератор путей ко всем архивам в указанной директории.

        Args:
            source_path: Путь к директории с архивами (локальный или S3 URI)

        Yields:
            str: Путь к архиву

        Raises:
            StorageError: При ошибках доступа к хранилищу
        """
        pass

    @abstractmethod
    def get_archive_metadata(self, archive_path: str) -> ArchiveMetadata:
        """Получает метаданные архива.

        Args:
            archive_path: Путь к архиву

        Returns:
            ArchiveMetadata: Метаданные архива

        Raises:
            StorageError: При ошибках доступа к архиву
        """
        pass

    @abstractmethod
    def list_books_in_archive(self, archive_path: str) -> list[str]:
        """Возвращает список имен книжных файлов внутри архива.

        Args:
            archive_path: Путь к архиву

        Returns:
            list[str]: Список имен файлов (например, ['12345.fb2', '67890.fb2'])

        Raises:
            StorageError: При ошибках чтения архива
        """
        pass


class StorageError(Exception):
    """Базовое исключение для ошибок работы с хранилищем."""

    def __init__(self, message: str, storage_type: str = "", path: str = ""):
        self.message = message
        self.storage_type = storage_type
        self.path = path
        super().__init__(f"[{storage_type}] {message} (path: {path})")


class StorageAccessError(StorageError):
    """Ошибка доступа к хранилищу (нет файла, нет прав доступа)."""

    pass


class StorageCorruptionError(StorageError):
    """Ошибка целостности данных в хранилище (поврежденный архив)."""

    pass
